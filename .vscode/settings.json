{"files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/.retool_types/**": true, "**/*tsconfig.json": true, ".cache": true, "retool.config.json": true}, "zencoder.mcpServers": {"Custom_MCP_server": {"command": "npx", "args": ["-y", "supergateway@latest", "--sse", "http://192.168.1.84:8765/mcp/openmemory/sse/aungheinaye"]}, "firecrawl": {"command": "npx", "args": ["firecrawl-mcp"], "env": {"FIRECRAWL_API_URL": "http://192.168.1.84:3002"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking@latest"]}, "mem0_Docs": {"command": "npx", "args": ["mcp-remote", "https://gitmcp.io/mem0ai/mem0"]}, "taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"ANTHROPIC_API_KEY": "************************************************************************************************************", "PERPLEXITY_API_KEY": "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", "OPENAI_API_KEY": "********************************************************", "GOOGLE_API_KEY": "AIzaSyCoxDZ4TpZjDZ-hr-Wn634_6ItmCFIl7i8", "MISTRAL_API_KEY": "5wEP8EG2qfHIAx4CAQPhYK6YzGT2TXy1", "OPENROUTER_API_KEY": "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", "XAI_API_KEY": "************************************************************************************"}, "alwaysAllow": ["update_task", "parse_prd", "add_task", "add_subtask", "get_tasks", "set_task_status", "get_task"]}}, "zencoder.enableRepoIndexing": true}