/**
 * Custom error classes for the MCP server
 */

export enum ErrorCode {
  // Protocol errors
  PROTOCOL_ERROR = 'PROTOCOL_ERROR',
  INVALID_REQUEST = 'INVALID_REQUEST',
  METHOD_NOT_FOUND = 'METHOD_NOT_FOUND',
  
  // Validation errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  SCHEMA_VALIDATION_ERROR = 'SCHEMA_VALIDATION_ERROR',
  PARAMETER_ERROR = 'PARAMETER_ERROR',
  
  // Database errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  QUERY_ERROR = 'QUERY_ERROR',
  
  // Tool errors
  TOOL_NOT_FOUND = 'TOOL_NOT_FOUND',
  TOOL_EXECUTION_ERROR = 'TOOL_EXECUTION_ERROR',
  TOOL_TIMEOUT = 'TOOL_TIMEOUT',
  
  // Configuration errors
  CONFIG_ERROR = 'CONFIG_ERROR',
  MISSING_CREDENTIALS = 'MISSING_CREDENTIALS',
  
  // Server errors
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
}

export class McpServerError extends Error {
  public readonly code: ErrorCode;
  public readonly statusCode: number;
  public readonly requestId?: string;
  public readonly context?: Record<string, unknown>;
  public readonly timestamp: Date;

  constructor(
    code: ErrorCode,
    message: string,
    statusCode: number = 500,
    options: {
      requestId?: string;
      context?: Record<string, unknown>;
      cause?: Error;
    } = {}
  ) {
    super(message);
    this.name = 'McpServerError';
    this.code = code;
    this.statusCode = statusCode;
    this.requestId = options.requestId;
    this.context = options.context;
    this.timestamp = new Date();
    
    if (options.cause) {
      this.cause = options.cause;
    }
    
    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, McpServerError);
    }
  }

  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      statusCode: this.statusCode,
      requestId: this.requestId,
      context: this.context,
      timestamp: this.timestamp.toISOString(),
      stack: this.stack,
    };
  }
}

export class ProtocolError extends McpServerError {
  constructor(message: string, options?: { requestId?: string; context?: Record<string, unknown>; cause?: Error }) {
    super(ErrorCode.PROTOCOL_ERROR, message, 400, options);
    this.name = 'ProtocolError';
  }
}

export class ValidationError extends McpServerError {
  constructor(message: string, options?: { requestId?: string; context?: Record<string, unknown>; cause?: Error }) {
    super(ErrorCode.VALIDATION_ERROR, message, 400, options);
    this.name = 'ValidationError';
  }
}

export class DatabaseError extends McpServerError {
  constructor(message: string, options?: { requestId?: string; context?: Record<string, unknown>; cause?: Error }) {
    super(ErrorCode.DATABASE_ERROR, message, 500, options);
    this.name = 'DatabaseError';
  }
}

export class ToolError extends McpServerError {
  constructor(message: string, code: ErrorCode = ErrorCode.TOOL_EXECUTION_ERROR, options?: { requestId?: string; context?: Record<string, unknown>; cause?: Error }) {
    super(code, message, 500, options);
    this.name = 'ToolError';
  }
}

export class ConfigurationError extends McpServerError {
  constructor(message: string, options?: { requestId?: string; context?: Record<string, unknown>; cause?: Error }) {
    super(ErrorCode.CONFIG_ERROR, message, 500, options);
    this.name = 'ConfigurationError';
  }
}

/**
 * Utility function to create standardized error responses
 */
export function createErrorResponse(error: unknown, requestId?: string) {
  if (error instanceof McpServerError) {
    return {
      isError: true,
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            error: {
              code: error.code,
              message: error.message,
              requestId: error.requestId || requestId,
              timestamp: error.timestamp.toISOString(),
              context: error.context,
            },
          }),
        },
      ],
    };
  }

  // Handle unknown errors
  const unknownError = new McpServerError(
    ErrorCode.INTERNAL_ERROR,
    error instanceof Error ? error.message : String(error),
    500,
    { requestId, cause: error instanceof Error ? error : undefined }
  );

  return {
    isError: true,
    content: [
      {
        type: 'text',
        text: JSON.stringify({
          error: {
            code: unknownError.code,
            message: unknownError.message,
            requestId: unknownError.requestId,
            timestamp: unknownError.timestamp.toISOString(),
          },
        }),
      },
    ],
  };
}

/**
 * Enhanced error enumeration function with better context
 */
export function enumerateError(error: unknown, requestId?: string): Record<string, unknown> | unknown {
  // Handle null/undefined by returning as-is
  if (error === null || error === undefined) {
    return error;
  }

  if (error instanceof McpServerError) {
    return {
      name: error.name,
      code: error.code,
      message: error.message,
      statusCode: error.statusCode,
      requestId: error.requestId || requestId,
      timestamp: error.timestamp.toISOString(),
      context: error.context,
    };
  }

  if (error instanceof Error) {
    return {
      name: error.name,
      message: error.message,
      requestId,
      timestamp: new Date().toISOString(),
    };
  }

  return {
    error: error,
    requestId,
    timestamp: new Date().toISOString(),
  };
}
