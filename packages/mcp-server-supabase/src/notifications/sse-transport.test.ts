import { describe, expect, test, beforeEach, afterEach, vi } from 'vitest';
import { IncomingMessage, ServerResponse } from 'http';
import { SSETransport } from './sse-transport.js';
import { ConfigManager } from '../config/config-manager.js';
import type { ProtocolMessage, NotificationPayload } from './index.js';

describe('SSETransport', () => {
  let transport: SSETransport;
  let mockConfig: ConfigManager;

  beforeEach(() => {
    mockConfig = {
      get: vi.fn((key: string, defaultValue?: any) => {
        const config: Record<string, any> = {
          SSE_PATH: '/events',
          SSE_MAX_CONNECTIONS: 100,
          SSE_HEARTBEAT_INTERVAL: 30000,
          SSE_CONNECTION_TIMEOUT: 300000,
          SSE_ENABLE_CORS: true,
          SSE_CORS_ORIGINS: ['*'],
          SSE_ENABLE_AUTH: false,
        };
        return config[key] ?? defaultValue;
      }),
    } as any;

    transport = new SSETransport({}, mockConfig);
  });

  afterEach(async () => {
    if (transport.isActive()) {
      await transport.stop();
    }
  });

  describe('Initialization', () => {
    test('should initialize with default options', () => {
      const defaultTransport = new SSETransport();
      expect(defaultTransport).toBeDefined();
    });

    test('should initialize with custom options', () => {
      const customTransport = new SSETransport({
        path: '/custom-events',
        maxConnections: 50,
        enableCors: false,
      });
      expect(customTransport).toBeDefined();
    });

    test('should use config values when provided', () => {
      expect(mockConfig.get).toHaveBeenCalledWith('SSE_PATH', '/events');
      expect(mockConfig.get).toHaveBeenCalledWith('SSE_MAX_CONNECTIONS', 100);
    });
  });

  describe('Transport Lifecycle', () => {
    test('should start transport successfully', async () => {
      await transport.start();
      expect(transport.isActive()).toBe(true);
    });

    test('should not start if already running', async () => {
      await transport.start();
      const firstStart = transport.isActive();
      
      // Try to start again
      await transport.start();
      expect(firstStart).toBe(true);
      expect(transport.isActive()).toBe(true);
    });

    test('should stop transport successfully', async () => {
      await transport.start();
      expect(transport.isActive()).toBe(true);
      
      await transport.stop();
      expect(transport.isActive()).toBe(false);
    });
  });

  describe('Connection Handling', () => {
    let mockRequest: Partial<IncomingMessage>;
    let mockResponse: Partial<ServerResponse>;

    beforeEach(async () => {
      mockRequest = {
        headers: {
          'user-agent': 'test-agent',
          origin: 'http://localhost:3000',
        },
        socket: {
          remoteAddress: '127.0.0.1',
        } as any,
      };

      mockResponse = {
        writeHead: vi.fn(),
        write: vi.fn(),
        end: vi.fn(),
        destroyed: false,
        on: vi.fn(),
      };

      await transport.start();
    });

    test('should handle new connection', () => {
      transport.handleConnection(mockRequest as IncomingMessage, mockResponse as ServerResponse);
      
      expect(mockResponse.writeHead).toHaveBeenCalledWith(200, expect.objectContaining({
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      }));
    });

    test('should reject connection when not running', async () => {
      await transport.stop();
      
      transport.handleConnection(mockRequest as IncomingMessage, mockResponse as ServerResponse);
      
      expect(mockResponse.writeHead).toHaveBeenCalledWith(503, { 'Content-Type': 'text/plain' });
      expect(mockResponse.end).toHaveBeenCalledWith('SSE transport not running');
    });

    test('should reject connection when at max capacity', async () => {
      // Set max connections to 0 for testing
      const limitedTransport = new SSETransport({ maxConnections: 0 });
      await limitedTransport.start();
      
      limitedTransport.handleConnection(mockRequest as IncomingMessage, mockResponse as ServerResponse);
      
      expect(mockResponse.writeHead).toHaveBeenCalledWith(503, { 'Content-Type': 'text/plain' });
      expect(mockResponse.end).toHaveBeenCalledWith('Server overloaded');
      
      await limitedTransport.stop();
    });

    test('should add CORS headers when enabled', () => {
      transport.handleConnection(mockRequest as IncomingMessage, mockResponse as ServerResponse);
      
      expect(mockResponse.writeHead).toHaveBeenCalledWith(200, expect.objectContaining({
        'Access-Control-Allow-Origin': 'http://localhost:3000',
        'Access-Control-Allow-Credentials': 'true',
      }));
    });

    test('should send initial connection event', () => {
      transport.handleConnection(mockRequest as IncomingMessage, mockResponse as ServerResponse);
      
      expect(mockResponse.write).toHaveBeenCalledWith(expect.stringContaining('event: connected'));
      expect(mockResponse.write).toHaveBeenCalledWith(expect.stringContaining('data: '));
    });
  });

  describe('Message Handling', () => {
    let mockClient: any;
    let mockResponse: Partial<ServerResponse>;

    beforeEach(async () => {
      mockResponse = {
        write: vi.fn(),
        end: vi.fn(),
        destroyed: false,
        on: vi.fn(),
      };

      mockClient = {
        id: 'test-client-1',
        response: mockResponse,
        request: {} as IncomingMessage,
        isActive: true,
        connectedAt: new Date(),
        lastActivity: new Date(),
        subscriptions: new Set(),
        metadata: {
          userAgent: 'test-agent',
          remoteAddress: '127.0.0.1',
        },
      };

      await transport.start();
      (transport as any).clients.set('test-client-1', mockClient);
    });

    test('should send message to specific client', async () => {
      const message: ProtocolMessage = {
        id: 'msg-1',
        type: 'notification',
        method: 'test',
        params: { data: 'test' },
        timestamp: new Date(),
      };

      await transport.sendMessage(message, 'test-client-1');
      
      expect(mockResponse.write).toHaveBeenCalledWith(expect.stringContaining('event: message'));
      expect(mockResponse.write).toHaveBeenCalledWith(expect.stringContaining('data: '));
    });

    test('should broadcast message to all clients', async () => {
      const message: ProtocolMessage = {
        id: 'msg-2',
        type: 'notification',
        method: 'broadcast',
        params: { data: 'broadcast' },
        timestamp: new Date(),
      };

      await transport.sendMessage(message);
      
      expect(mockResponse.write).toHaveBeenCalledWith(expect.stringContaining('event: message'));
    });

    test('should send notification', async () => {
      const notification: NotificationPayload = {
        id: 'notif-1',
        type: 'test.notification',
        timestamp: new Date(),
        source: 'test',
        data: { message: 'test notification' },
        priority: 'medium',
      };

      await transport.sendNotification(notification, 'test-client-1');
      
      expect(mockResponse.write).toHaveBeenCalledWith(expect.stringContaining('event: message'));
    });

    test('should skip sending to inactive clients', async () => {
      mockClient.isActive = false;

      const message: ProtocolMessage = {
        id: 'msg-inactive',
        type: 'notification',
        method: 'test',
        params: {},
        timestamp: new Date(),
      };

      await transport.sendMessage(message, 'test-client-1');
      
      // Should not write to inactive client
      expect(mockResponse.write).not.toHaveBeenCalled();
    });

    test('should handle write errors gracefully', async () => {
      (mockResponse.write as any).mockImplementation(() => {
        throw new Error('Write failed');
      });

      const message: ProtocolMessage = {
        id: 'msg-error',
        type: 'notification',
        method: 'test',
        params: {},
        timestamp: new Date(),
      };

      // Should not throw
      await expect(transport.sendMessage(message, 'test-client-1')).resolves.not.toThrow();
    });
  });

  describe('Client Management', () => {
    test('should get all clients', () => {
      const mockClient = {
        id: 'client-1',
        response: {} as ServerResponse,
        request: {} as IncomingMessage,
        isActive: true,
        connectedAt: new Date(),
        lastActivity: new Date(),
        subscriptions: new Set(),
        metadata: {},
      };

      (transport as any).clients.set('client-1', mockClient);
      
      const clients = transport.getClients();
      expect(clients).toHaveLength(1);
      expect(clients[0].id).toBe('client-1');
    });

    test('should get client by ID', () => {
      const mockClient = {
        id: 'client-2',
        response: {} as ServerResponse,
        request: {} as IncomingMessage,
        isActive: true,
        connectedAt: new Date(),
        lastActivity: new Date(),
        subscriptions: new Set(),
        metadata: {},
      };

      (transport as any).clients.set('client-2', mockClient);
      
      const client = transport.getClient('client-2');
      expect(client).toBeDefined();
      expect(client?.id).toBe('client-2');
    });

    test('should return undefined for non-existent client', () => {
      const client = transport.getClient('non-existent');
      expect(client).toBeUndefined();
    });
  });

  describe('Statistics', () => {
    test('should return correct stats', () => {
      const stats = transport.getStats();
      
      expect(stats).toEqual({
        isRunning: false,
        connectedClients: 0,
        totalConnections: 0,
        path: '/events',
      });
    });

    test('should update stats when transport is running', async () => {
      await transport.start();
      
      const stats = transport.getStats();
      expect(stats.isRunning).toBe(true);
    });
  });

  describe('Event Emission', () => {
    test('should emit started event when transport starts', async () => {
      const startedHandler = vi.fn();
      transport.on('started', startedHandler);

      await transport.start();
      
      expect(startedHandler).toHaveBeenCalled();
    });

    test('should emit stopped event when transport stops', async () => {
      const stoppedHandler = vi.fn();
      transport.on('stopped', stoppedHandler);

      await transport.start();
      await transport.stop();
      
      expect(stoppedHandler).toHaveBeenCalled();
    });
  });
});
